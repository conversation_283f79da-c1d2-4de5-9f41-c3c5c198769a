'use client';

import CFSServiceRequests from "@/components/services/cfs/service_requests/CFSServiceRequests";
import { useSidebar } from "@/contexts/SidebarProvider";
import { useEffect } from "react";

export default function EIRPage() {
	const { setTitle } = useSidebar();
	useEffect(() => {
		setTitle('EIR Copy');
	}, []);

	return (
		<section className="grid gap-8 min-h-dvh">
			<CFSServiceRequests serviceName="EIR Copy" />
		</section>
	)
}

