'use client';

import { ROLES } from '@/constants/roles';
import { useAuth } from '@/contexts/AuthContext';
import { useChat } from '@/hooks/useChat';
import {
  Bell,
  CheckCircle,
  Clock,
  Filter,
  MessageCircle,
  MessageSquare,
  Search,
  Users,
  XCircle
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

/**
 * GOL Support Dashboard
 * Shows all customer chat sessions for GOL staff to manage
 */
export default function GOLSupportPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const { getAllChatSessions, assignAgentToSession } = useChat();

  const [allSessions, setAllSessions] = useState([]);
  const [filteredSessions, setFilteredSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [assigningSession, setAssigningSession] = useState(null);

  // Load all chat sessions for GOL staff
  const loadAllSessions = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const sessions = await getAllChatSessions();
      setAllSessions(sessions);
      setFilteredSessions(sessions);
    } catch (error) {
      console.error('Error loading sessions:', error);
      toast.error('Failed to load chat sessions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!authLoading) {
      if (!user) {
        toast.error('Please log in to access support');
        router.push('/login');
        return;
      }

      // Only allow GOL staff to access this dashboard
      if (![ROLES.GOL_STAFF, ROLES.GOL_MOD, ROLES.ROOT].includes(user.role)) {
        toast.error('Access denied. This page is for GOL staff only.');
        router.push('/dashboard');
        return;
      }

      loadAllSessions();
    }
  }, [user, authLoading, router]);

  // Filter sessions based on search and status
  useEffect(() => {
    let filtered = [...allSessions];

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(session => {
        const customerName = session.expand?.customer?.firstname ||
          session.expand?.customer?.username ||
          session.expand?.customer?.email || '';
        const sessionId = session.id || '';
        const subject = session.subject || '';

        return customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          sessionId.toLowerCase().includes(searchQuery.toLowerCase()) ||
          subject.toLowerCase().includes(searchQuery.toLowerCase());
      });
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(session => session.status === statusFilter);
    }

    setFilteredSessions(filtered);
  }, [allSessions, searchQuery, statusFilter]);

  const handleAssignToMe = async (sessionId) => {
    setAssigningSession(sessionId);
    try {
      await assignAgentToSession(sessionId, user.id);
      // Refresh sessions to show updated assignment
      loadAllSessions();
    } catch (error) {
      console.error('Error assigning session:', error);
    } finally {
      setAssigningSession(null);
    }
  };

  const handleOpenChat = (sessionId) => {
    router.push(`/gol/support/chat/${sessionId}`);
  };

  const getSessionStatusIcon = (status) => {
    switch (status) {
      case 'Open':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'Close':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getTimeSince = (dateString) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const getSessionPriority = (session) => {
    // Determine priority based on how long the session has been waiting
    const now = new Date();
    const created = new Date(session.created);
    const hoursWaiting = (now - created) / (1000 * 60 * 60);

    if (hoursWaiting > 24) return 'high';
    if (hoursWaiting > 4) return 'medium';
    return 'low';
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-green-100 text-green-800';
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-accent shadow-sm border-b border-primary/20">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-foreground">GOL Support Dashboard</h1>
              <p className="text-foreground/70">Manage customer support requests and chat sessions</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-primary" />
                <span className="text-sm text-foreground/70">
                  {filteredSessions.length} Sessions
                </span>
              </div>
              <span className="text-sm text-foreground/70">
                {user?.username || user?.email}
              </span>
              <span className="px-2 py-1 bg-primary text-accent text-xs rounded-full">
                {user?.role}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters and Search */}
        <div className="bg-accent rounded-lg shadow-sm border border-primary/20 p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search by customer name, session ID, or subject..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-foreground"
                />
              </div>
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent bg-background text-foreground"
            >
              <option value="all">All Status</option>
              <option value="Open">Open</option>
              <option value="Close">Closed</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">

            {/* Customer Chat Sessions */}
            <div className="bg-accent rounded-lg shadow-sm border border-primary/20">
              <div className="p-6 border-b border-primary/20">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-lg font-semibold text-foreground">Customer Chat Sessions</h2>
                    <p className="text-foreground/70">Manage and respond to customer support requests</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <MessageCircle className="w-5 h-5 text-primary" />
                    <span className="text-sm text-foreground/70">
                      {filteredSessions.filter(s => s.status === 'Open').length} Active
                    </span>
                  </div>
                </div>
              </div>

              {loading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-foreground/70">Loading chat sessions...</p>
                </div>
              ) : filteredSessions.length === 0 ? (
                <div className="p-12 text-center text-gray-500">
                  <MessageSquare className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No chat sessions found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchQuery || statusFilter !== 'all'
                      ? 'Try adjusting your filters to see more sessions'
                      : 'Customer chat sessions will appear here when they start conversations'
                    }
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {filteredSessions.map((session) => {
                    const customer = session.expand?.user || {};
                    const agent = session.expand?.agent || {};
                    const priority = getSessionPriority(session);
                    const isAssigned = !!session.agent;
                    const isAssignedToMe = session.agent === user?.id;

                    return (
                      <div
                        key={session.id}
                        className="p-6 hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-2">
                              {getSessionStatusIcon(session.status)}
                              <span className="font-medium text-gray-900">
                                {customer.firstname || customer.username || customer.email || 'Unknown Customer'}
                              </span>
                              <span className={`px-2 py-1 text-xs rounded-full ${session.status === 'Open'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                                }`}>
                                {session.status}
                              </span>
                              <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(priority)}`}>
                                {priority.toUpperCase()} PRIORITY
                              </span>
                            </div>

                            <div className="text-sm text-gray-600 space-y-1">
                              <p><strong>Subject:</strong> {session.subject || 'General Inquiry'}</p>
                              <p><strong>Session ID:</strong> {session.id}</p>
                              <p><strong>Created:</strong> {getTimeSince(session.created)}</p>
                              {isAssigned && (
                                <p><strong>Assigned to:</strong> {agent.username || 'Agent'}
                                  {isAssignedToMe && <span className="text-primary"> (You)</span>}
                                </p>
                              )}
                              {session.status === 'Close' && session.closed_at && (
                                <p><strong>Closed:</strong> {getTimeSince(session.closed_at)}</p>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            {!isAssigned && session.status === 'Open' && (
                              <button
                                onClick={() => handleAssignToMe(session.id)}
                                disabled={assigningSession === session.id}
                                className="hover:cursor-pointer px-3 py-1 bg-primary text-accent text-sm lg:text-lg rounded hover:bg-primary/90 disabled:opacity-50"
                              >
                                {assigningSession === session.id ? 'Assigning...' : 'Assign'}
                              </button>
                            )}
                            <button
                              onClick={() => handleOpenChat(session.id)}
                              className="flex items-center hover:cursor-pointer px-3 py-1 bg-secondary text-accent text-sm lg:text-lg rounded hover:bg-secondary/90"
                            >
                              <MessageCircle className="w-3 lg:w-6 h-3 lg:h-6 mr-1" />
                              Open Chat
                            </button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>


          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Session Statistics */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Session Statistics</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <span className="font-medium text-green-900">Open Sessions</span>
                  </div>
                  <span className="text-lg font-bold text-green-600">
                    {filteredSessions.filter(s => s.status === 'Open').length}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <XCircle className="w-5 h-5 text-red-600" />
                    <span className="font-medium text-red-900">Closed Sessions</span>
                  </div>
                  <span className="text-lg font-bold text-red-600">
                    {filteredSessions.filter(s => s.status === 'Close').length}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Clock className="w-5 h-5 text-yellow-600" />
                    <span className="font-medium text-yellow-900">Unassigned</span>
                  </div>
                  <span className="text-lg font-bold text-yellow-600">
                    {filteredSessions.filter(s => !s.agent && s.status === 'Open').length}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Users className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-900">My Sessions</span>
                  </div>
                  <span className="text-lg font-bold text-blue-600">
                    {filteredSessions.filter(s => s.agent === user?.id).length}
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button
                  onClick={() => setStatusFilter('Open')}
                  className="w-full flex items-center space-x-3 p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
                >
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-900">View Open Sessions</span>
                </button>

                <button
                  onClick={() => {
                    setStatusFilter('Open');
                    setSearchQuery('');
                  }}
                  className="w-full flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors"
                >
                  <Bell className="w-5 h-5 text-yellow-600" />
                  <span className="font-medium text-yellow-900">Show Unassigned</span>
                </button>

                <button
                  onClick={() => {
                    setStatusFilter('all');
                    setSearchQuery('');
                  }}
                  className="w-full flex items-center space-x-3 p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <Filter className="w-5 h-5 text-blue-600" />
                  <span className="font-medium text-blue-900">Clear Filters</span>
                </button>
              </div>
            </div>

            {/* Support Guidelines */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Support Guidelines</h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="font-medium text-blue-900 mb-1">Response Time Goals</p>
                  <p className="text-blue-700">• High Priority: &lt; 1 hour</p>
                  <p className="text-blue-700">• Medium Priority: &lt; 4 hours</p>
                  <p className="text-blue-700">• Low Priority: &lt; 24 hours</p>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="font-medium text-green-900 mb-1">Best Practices</p>
                  <p className="text-green-700">• Assign yourself to sessions you handle</p>
                  <p className="text-green-700">• Update session status when resolved</p>
                  <p className="text-green-700">• Provide clear and helpful responses</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
